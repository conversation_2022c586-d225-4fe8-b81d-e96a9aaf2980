用HTML、CSS和JavaScript复现图中宇宙引力模拟器的所有功能，要求天体运动符合万有引力定律，最终所有内容放在一个html里

我需要先查看当前的HTML文件来了解具体的实现细节，然后为你生成一个合适的prompt。

基于这个宇宙引力小游戏的代码，我为你生成一个适合给LLM出题的prompt：

---

推理过程：

1）采用单页面HTML结构，左侧控制面板+右侧帮助说明+中央Canvas画布，并通过CSS变量统一深色太空主题配色；
2）建立世界坐标与屏幕坐标的转换机制，实现平移和缩放；
3）采用经典万有引力公式F=GMm/r²，计算所有天体间作用力，同时添加软化参数避免距离过近时的奇点问题，用欧拉积分更新位置和速度；
4）监听鼠标事件实现左键拖拽平移、滚轮缩放、右键拖拽创建天体，通过状态机管理不同交互模式；
5）分层绘制背景星空、轨迹拖尾、天体本体和临时放置预览，天体使用径向渐变实现发光效果，轨迹用透明度渐变显示历史路径；
6）通过滑块和开关实时调节物理参数，双向绑定更新显示值，提供暂停、重置、清空和示例系统等功能按钮；
7）使用requestAnimationFrame循环，限制最大时间步长保证稳定性，实现FPS计数器监控性能表现；

得分点：需用题中要求的语言，包含题目要求的算法（如有），且代码在 IDE（集成开发环境）中可正确运行并输出符合题意的结果；