import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from matplotlib import cm
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.family'] = 'SimHei'
plt.rcParams['axes.unicode_minus'] = False

def sigmoid(x):
    """Sigmoid激活函数"""
    return 1 / (1 + np.exp(-np.clip(x, -500, 500)))

def tanh_activation(x):
    """Tanh激活函数"""
    return np.tanh(x)

def relu(x):
    """ReLU激活函数"""
    return np.maximum(0, x)

def swish(x):
    """Swish激活函数"""
    return x * sigmoid(x)

def complex_activation_landscape(x, y):
    """
    复合激活函数景观 - 模拟神经网络中多层激活函数的组合效果
    这个函数结合了多种激活函数，模拟深度学习中的复杂非线性变换
    """
    # 第一层：输入预处理
    r = np.sqrt(x**2 + y**2)
    theta = np.arctan2(y, x)
    
    # 第二层：径向基础变换
    radial_component = np.exp(-0.3 * r) * np.cos(2 * theta)
    
    # 第三层：Sigmoid门控机制
    gate1 = sigmoid(2 * x - 1)
    gate2 = sigmoid(2 * y + 0.5)
    
    # 第四层：Tanh非线性变换
    nonlinear_x = tanh_activation(1.5 * x + 0.5 * y)
    nonlinear_y = tanh_activation(1.5 * y - 0.5 * x)
    
    # 第五层：ReLU激活区域
    relu_region = relu(x + y - 1) * relu(2 - x - y)
    
    # 第六层：Swish平滑变换
    swish_x = swish(0.8 * x)
    swish_y = swish(0.8 * y)
    
    # 最终组合：多层激活函数的复合效果
    z = (0.4 * radial_component * gate1 * gate2 + 
         0.3 * nonlinear_x * nonlinear_y + 
         0.2 * relu_region + 
         0.1 * (swish_x + swish_y))
    
    # 添加高频细节（模拟深层网络的复杂特征）
    high_freq = 0.05 * np.sin(8 * x) * np.cos(8 * y) * np.exp(-0.1 * r)
    
    return z + high_freq

def create_3d_visualization():
    """创建3D可视化图形"""
    # 创建网格
    x = np.linspace(-5, 5, 100)
    y = np.linspace(-5, 5, 100)
    X, Y = np.meshgrid(x, y)
    
    # 计算复合激活函数值
    Z = complex_activation_landscape(X, Y)
    
    # 创建3D图形
    fig = plt.figure(figsize=(12, 10))
    ax = fig.add_subplot(111, projection='3d')
    
    # 绘制3D表面
    surf = ax.plot_surface(X, Y, Z, cmap='viridis', alpha=0.9, 
                          linewidth=0, antialiased=True, shade=True)
    
    # 添加等高线投影
    contours = ax.contour(X, Y, Z, levels=15, cmap='plasma', alpha=0.6, offset=-1)
    
    # 设置标签和标题
    ax.set_xlabel('X', fontsize=12, labelpad=10)
    ax.set_ylabel('Y', fontsize=12, labelpad=10)
    ax.set_zlabel('Z', fontsize=12, labelpad=10)
    ax.set_title('复合激活函数景观', fontsize=16, pad=20)
    
    # 设置视角
    ax.view_init(elev=25, azim=45)
    
    # 添加颜色条
    fig.colorbar(surf, ax=ax, shrink=0.5, aspect=20, pad=0.1)
    
    # 设置网格
    ax.grid(True, alpha=0.3)
    
    # 调整布局
    plt.tight_layout()
    
    return fig, ax

def analyze_critical_points():
    """分析关键点和梯度信息"""
    # 创建更精细的网格用于分析
    x = np.linspace(-5, 5, 200)
    y = np.linspace(-5, 5, 200)
    X, Y = np.meshgrid(x, y)
    Z = complex_activation_landscape(X, Y)
    
    # 计算梯度（数值微分）
    dx = x[1] - x[0]
    dy = y[1] - y[0]
    
    grad_x = np.gradient(Z, dx, axis=1)
    grad_y = np.gradient(Z, dy, axis=0)
    
    # 计算梯度幅值
    grad_magnitude = np.sqrt(grad_x**2 + grad_y**2)
    
    # 寻找局部极值点（梯度接近零的点）
    threshold = 0.01
    critical_mask = grad_magnitude < threshold
    
    # 找到关键点的坐标
    critical_indices = np.where(critical_mask)
    critical_x = X[critical_indices]
    critical_y = Y[critical_indices]
    critical_z = Z[critical_indices]
    
    return critical_x, critical_y, critical_z, grad_magnitude

if __name__ == "__main__":
    # 生成3D可视化
    fig, ax = create_3d_visualization()
    
    # 分析关键点
    crit_x, crit_y, crit_z, grad_mag = analyze_critical_points()
    
    # 在3D图上标记一些重要的关键点
    if len(crit_x) > 0:
        # 选择Z值最高和最低的几个点
        max_indices = np.argsort(crit_z)[-3:]  # 最高的3个点
        min_indices = np.argsort(crit_z)[:3]   # 最低的3个点
        
        # 标记最高点
        ax.scatter(crit_x[max_indices], crit_y[max_indices], crit_z[max_indices], 
                  c='red', s=100, alpha=0.8, label='局部最大值')
        
        # 标记最低点
        ax.scatter(crit_x[min_indices], crit_y[min_indices], crit_z[min_indices], 
                  c='blue', s=100, alpha=0.8, label='局部最小值')
        
        ax.legend()
    
    # 保存图像
    plt.savefig('C:/Users/<USER>/OneDrive/Desktop/VLM prompt/VLM-code/code/26/complex_activation_landscape.png', 
                dpi=300, bbox_inches='tight')
    plt.show()
    
    # 输出一些统计信息
    print(f"函数值范围: [{np.min(complex_activation_landscape(np.linspace(-5,5,100), np.linspace(-5,5,100))):.3f}, "
          f"{np.max(complex_activation_landscape(np.linspace(-5,5,100), np.linspace(-5,5,100))):.3f}]")
    print(f"发现 {len(crit_x)} 个关键点")
    if len(crit_x) > 0:
        print(f"最高点Z值: {np.max(crit_z):.3f}")
        print(f"最低点Z值: {np.min(crit_z):.3f}")
