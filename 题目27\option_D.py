import numpy as np
import matplotlib.pyplot as plt
from matplotlib.colors import LinearSegmentedColormap

plt.rcParams['font.family'] = 'SimHei'
plt.rcParams['axes.unicode_minus'] = False

def sinusoidal_positional_encoding_D(seq_len, d_model):
    pe = np.zeros((seq_len, d_model))
    position = np.arange(0, seq_len).reshape(-1, 1)
    
    div_term = np.exp(np.arange(0, d_model, 2) * -(np.log(10000.0) / d_model))
    
    pe[:, 0::2] = np.sin(position * div_term)
    pe[:, 1::2] = np.cos(position * div_term)
    
    return pe

def create_visualization_D():
    seq_len = 128
    d_model = 64
    
    pe = sinusoidal_positional_encoding_D(seq_len, d_model)
    
    fig = plt.figure(figsize=(5, 4))
    
    colors = ['#2E86AB', '#A23B72', '#F18F01', '#C73E1D']
    custom_cmap = LinearSegmentedColormap.from_list('custom', colors, N=256)
    
    ax1 = plt.subplot(3, 4, (1,12))
    im1 = ax1.imshow(pe.T, cmap=custom_cmap, aspect='auto', interpolation='bilinear')
    ax1.set_title('正弦位置编码\n(Sinusoidal PE)', fontsize=12, fontweight='bold')
    ax1.set_xlabel('序列位置')
    ax1.set_ylabel('嵌入维度')
    plt.colorbar(im1, ax=ax1, shrink=0.8)
    
    plt.tight_layout()
    plt.savefig("C:/Users/<USER>/OneDrive/Desktop/VLM prompt/VLM-code/code/题目27/Option_D.png")
    plt.show()
    
    return pe

if __name__ == "__main__":
    pe_D = create_visualization_D()
    print(f"选项D位置编码形状: {pe_D.shape}")
