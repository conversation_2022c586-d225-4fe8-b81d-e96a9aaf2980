import numpy as np
import matplotlib.pyplot as plt
import torch
import torch.nn as nn
import torch.optim as optim
from matplotlib.patches import Rectangle
import seaborn as sns
from mpl_toolkits.mplot3d import Axes3D
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.family'] = 'SimHei'
plt.rcParams['axes.unicode_minus'] = False

# 设置随机种子
torch.manual_seed(42)
np.random.seed(42)

class SimpleGenerator(nn.Module):
    def __init__(self, noise_dim=100, output_dim=784):
        super(SimpleGenerator, self).__init__()
        self.net = nn.Sequential(
            nn.Linear(noise_dim, 256),
            nn.ReLU(),
            nn.Linear(256, 512),
            nn.ReLU(),
            nn.Linear(512, output_dim),
            nn.Tanh()
        )
    
    def forward(self, x):
        return self.net(x)

class SimpleDiscriminator(nn.Module):
    def __init__(self, input_dim=784):
        super(SimpleDiscriminator, self).__init__()
        self.net = nn.Sequential(
            nn.Linear(input_dim, 512),
            nn.LeakyReLU(0.2),
            nn.Linear(512, 256),
            nn.LeakyReLU(0.2),
            nn.Linear(256, 1),
            nn.Sigmoid()
        )
    
    def forward(self, x):
        return self.net(x)

# 创建模型
generator = SimpleGenerator()
discriminator = SimpleDiscriminator()

# 损失函数和优化器
criterion = nn.BCELoss()
g_optimizer = optim.Adam(generator.parameters(), lr=0.0002, betas=(0.5, 0.999))
d_optimizer = optim.Adam(discriminator.parameters(), lr=0.0002, betas=(0.5, 0.999))

# 模拟训练数据
batch_size = 64
noise_dim = 100

# 生成真实数据（模拟MNIST数据分布）
def generate_real_data(batch_size):
    # 模拟真实数据分布：两个高斯混合
    data1 = torch.randn(batch_size//2, 784) * 0.3 + 0.5
    data2 = torch.randn(batch_size//2, 784) * 0.3 - 0.5
    return torch.cat([data1, data2], dim=0)

# 训练过程记录
epochs = 200
g_losses = []
d_losses = []
d_real_acc = []
d_fake_acc = []
js_divergence = []

# 模拟训练过程
for epoch in range(epochs):
    # 训练判别器
    discriminator.zero_grad()
    
    # 真实数据
    real_data = generate_real_data(batch_size)
    real_labels = torch.ones(batch_size, 1)
    real_output = discriminator(real_data)
    d_real_loss = criterion(real_output, real_labels)
    
    # 生成假数据
    noise = torch.randn(batch_size, noise_dim)
    fake_data = generator(noise)
    fake_labels = torch.zeros(batch_size, 1)
    fake_output = discriminator(fake_data.detach())
    d_fake_loss = criterion(fake_output, fake_labels)
    
    d_loss = d_real_loss + d_fake_loss
    d_loss.backward()
    d_optimizer.step()
    
    # 训练生成器
    generator.zero_grad()
    fake_output = discriminator(fake_data)
    g_loss = criterion(fake_output, real_labels)  # 生成器希望判别器认为假数据是真的
    g_loss.backward()
    g_optimizer.step()
    
    # 记录损失和准确率
    g_losses.append(g_loss.item())
    d_losses.append(d_loss.item())
    
    # 计算准确率
    d_real_acc.append((real_output > 0.5).float().mean().item())
    d_fake_acc.append((fake_output < 0.5).float().mean().item())
    
    # 计算JS散度（近似）
    p_real = real_output.mean().item()
    p_fake = fake_output.mean().item()
    js_div = 0.5 * (p_real * np.log(2 * p_real / (p_real + p_fake + 1e-8)) + 
                    p_fake * np.log(2 * p_fake / (p_real + p_fake + 1e-8)))
    js_divergence.append(abs(js_div))

# 创建复杂的可视化图表
fig = plt.figure(figsize=(20, 16))

# 1. 损失函数曲线（左上）
ax1 = plt.subplot(3, 4, 1)
plt.plot(g_losses, label='生成器损失', color='red', linewidth=2, alpha=0.8)
plt.plot(d_losses, label='判别器损失', color='blue', linewidth=2, alpha=0.8)
plt.xlabel('训练轮次')
plt.ylabel('损失值')
plt.title('GAN训练损失曲线')
plt.legend()
plt.grid(True, alpha=0.3)

# 2. 判别器准确率（右上）
ax2 = plt.subplot(3, 4, 2)
plt.plot(d_real_acc, label='真实数据准确率', color='green', linewidth=2)
plt.plot(d_fake_acc, label='假数据准确率', color='orange', linewidth=2)
plt.axhline(y=0.5, color='black', linestyle='--', alpha=0.5, label='理想平衡点')
plt.xlabel('训练轮次')
plt.ylabel('准确率')
plt.title('判别器分类准确率')
plt.legend()
plt.grid(True, alpha=0.3)

# 3. JS散度变化（左中）
ax3 = plt.subplot(3, 4, 3)
plt.plot(js_divergence, color='purple', linewidth=2)
plt.xlabel('训练轮次')
plt.ylabel('JS散度')
plt.title('Jensen-Shannon散度变化')
plt.grid(True, alpha=0.3)

# 4. 损失比值分析（右中）
ax4 = plt.subplot(3, 4, 4)
loss_ratio = np.array(g_losses) / (np.array(d_losses) + 1e-8)
plt.plot(loss_ratio, color='brown', linewidth=2)
plt.axhline(y=1.0, color='red', linestyle='--', alpha=0.7, label='平衡线')
plt.xlabel('训练轮次')
plt.ylabel('G_loss / D_loss')
plt.title('生成器/判别器损失比值')
plt.legend()
plt.grid(True, alpha=0.3)

# 5. 3D损失景观（左下，占2格）
ax5 = plt.subplot(3, 4, (5, 6), projection='3d')
epochs_mesh = np.arange(0, len(g_losses), 5)
g_loss_mesh = np.array(g_losses)[::5]
d_loss_mesh = np.array(d_losses)[::5]
X, Y = np.meshgrid(epochs_mesh[:len(g_loss_mesh)], epochs_mesh[:len(d_loss_mesh)])
Z = np.outer(g_loss_mesh, d_loss_mesh)[:len(epochs_mesh), :len(epochs_mesh)]

surf = ax5.plot_surface(X, Y, Z, cmap='viridis', alpha=0.8)
ax5.set_xlabel('生成器损失轮次')
ax5.set_ylabel('判别器损失轮次')
ax5.set_zlabel('损失乘积')
ax5.set_title('GAN损失景观3D视图')

# 6. 梯度范数分析（右下上）
ax6 = plt.subplot(3, 4, 7)
# 模拟梯度范数
g_grad_norm = np.array(g_losses) * np.random.uniform(0.8, 1.2, len(g_losses)) + np.random.normal(0, 0.1, len(g_losses))
d_grad_norm = np.array(d_losses) * np.random.uniform(0.9, 1.1, len(d_losses)) + np.random.normal(0, 0.05, len(d_losses))

plt.semilogy(g_grad_norm, label='生成器梯度范数', color='red', alpha=0.7)
plt.semilogy(d_grad_norm, label='判别器梯度范数', color='blue', alpha=0.7)
plt.xlabel('训练轮次')
plt.ylabel('梯度范数 (log scale)')
plt.title('梯度范数变化')
plt.legend()
plt.grid(True, alpha=0.3)

# 7. 模式坍塌检测（右下下）
ax7 = plt.subplot(3, 4, 8)
# 模拟生成样本多样性指标
diversity_score = 1.0 - np.exp(-np.array(js_divergence) * 2)
plt.plot(diversity_score, color='magenta', linewidth=2)
plt.axhline(y=0.8, color='green', linestyle='--', alpha=0.7, label='健康阈值')
plt.axhline(y=0.3, color='red', linestyle='--', alpha=0.7, label='坍塌阈值')
plt.xlabel('训练轮次')
plt.ylabel('样本多样性分数')
plt.title('模式坍塌检测')
plt.legend()
plt.grid(True, alpha=0.3)

# 8. 训练稳定性热力图（下方中间2格）
ax8 = plt.subplot(3, 4, (9, 10))
# 创建稳定性矩阵
stability_matrix = np.zeros((10, 20))
for i in range(10):
    for j in range(20):
        epoch_idx = i * 20 + j
        if epoch_idx < len(g_losses):
            # 稳定性指标：损失变化率 + 准确率平衡度
            loss_stability = 1.0 / (1.0 + abs(g_losses[epoch_idx] - d_losses[epoch_idx]))
            acc_balance = 1.0 - abs(d_real_acc[epoch_idx] - d_fake_acc[epoch_idx])
            stability_matrix[i, j] = (loss_stability + acc_balance) / 2

im = plt.imshow(stability_matrix, cmap='RdYlBu', aspect='auto')
plt.colorbar(im, label='稳定性分数')
plt.xlabel('训练子阶段')
plt.ylabel('训练大阶段')
plt.title('GAN训练稳定性热力图')

# 9. 收敛分析（下方右）
ax9 = plt.subplot(3, 4, 11)
# 计算移动平均
window = 20
g_loss_ma = np.convolve(g_losses, np.ones(window)/window, mode='valid')
d_loss_ma = np.convolve(d_losses, np.ones(window)/window, mode='valid')

plt.plot(g_loss_ma, label='生成器损失(MA)', color='red', linewidth=3)
plt.plot(d_loss_ma, label='判别器损失(MA)', color='blue', linewidth=3)
plt.fill_between(range(len(g_loss_ma)), g_loss_ma - np.std(g_losses), 
                 g_loss_ma + np.std(g_losses), alpha=0.2, color='red')
plt.fill_between(range(len(d_loss_ma)), d_loss_ma - np.std(d_losses), 
                 d_loss_ma + np.std(d_losses), alpha=0.2, color='blue')
plt.xlabel('训练轮次')
plt.ylabel('损失值')
plt.title('收敛趋势分析(移动平均)')
plt.legend()
plt.grid(True, alpha=0.3)

# 10. Nash均衡分析（下方最右）
ax10 = plt.subplot(3, 4, 12)
# Nash均衡指标
nash_equilibrium = []
for i in range(len(g_losses)):
    # Nash均衡：两个玩家的损失都相对稳定
    if i >= 10:
        g_variance = np.var(g_losses[i-10:i])
        d_variance = np.var(d_losses[i-10:i])
        nash_score = 1.0 / (1.0 + g_variance + d_variance)
        nash_equilibrium.append(nash_score)
    else:
        nash_equilibrium.append(0)

plt.plot(nash_equilibrium, color='darkgreen', linewidth=2)
plt.axhline(y=0.7, color='gold', linestyle='--', alpha=0.7, label='Nash均衡阈值')
plt.xlabel('训练轮次')
plt.ylabel('Nash均衡分数')
plt.title('Nash均衡收敛分析')
plt.legend()
plt.grid(True, alpha=0.3)

plt.tight_layout()
plt.savefig('23/gan_training_analysis.png', dpi=300, bbox_inches='tight')
plt.show()
