import numpy as np
import matplotlib.pyplot as plt
import threading
import time
from concurrent.futures import ThreadPoolExecutor
import queue
from matplotlib.patches import Circle



class MonteCarloPI:
    def __init__(self, n_threads=4):
        self.n_threads = n_threads
        self.results_queue = queue.Queue()
        self.points_inside = 0
        self.points_total = 0
        self.all_points_x = []
        self.all_points_y = []
        self.pi_estimates = []
        self.thread_colors = ['blue','red','green', 'pink','orange', 'purple', 'brown']
        
    def worker_thread(self, thread_id, n_points_per_thread):
        """Each thread independently calculates a portion of points"""
        np.random.seed(thread_id * 123 + 456)  # Fixed seeds for reproducible results
        
        points_x = np.random.uniform(-1, 1, n_points_per_thread)
        points_y = np.random.uniform(-1, 1, n_points_per_thread)
        
        # Calculate distance from origin
        distances = np.sqrt(points_x**2 + points_y**2)
        inside_circle = distances <= 1.0

        # Put results into queue
        self.results_queue.put({
            'thread_id': thread_id,
            'points_x': points_x,
            'points_y': points_y,
            'inside_circle': inside_circle,
            'count_inside': np.sum(inside_circle),
            'total_points': n_points_per_thread
        })
        
    def run_simulation(self, total_points=10000):
        """Run multi-threaded Monte Carlo simulation"""
        points_per_thread = total_points // self.n_threads

        # Start threads
        with ThreadPoolExecutor(max_workers=self.n_threads) as executor:
            futures = []
            for i in range(self.n_threads):
                future = executor.submit(self.worker_thread, i, points_per_thread)
                futures.append(future)

            # Wait for all threads to complete
            for future in futures:
                future.result()

        # Collect results
        thread_results = []
        while not self.results_queue.empty():
            thread_results.append(self.results_queue.get())

        return thread_results
    
    def visualize_results(self, thread_results):
        """Visualize Monte Carlo simulation results"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))

        # Top left: Distribution of all points (colored by thread)
        ax1 = axes[0, 0]
        circle = Circle((0, 0), 1, fill=False, color='black', linewidth=2)
        ax1.add_patch(circle)

        total_inside = 0
        total_points = 0

        for result in thread_results:
            tid = result['thread_id']
            color = self.thread_colors[tid % len(self.thread_colors)]

            # Points inside the circle
            inside_mask = result['inside_circle']
            ax1.scatter(result['points_x'][inside_mask],
                       result['points_y'][inside_mask],
                       c=color, alpha=0.6, s=2, label=f'Thread{tid}(inside)')

            # Points outside the circle
            outside_mask = ~inside_mask
            ax1.scatter(result['points_x'][outside_mask],
                       result['points_y'][outside_mask],
                       c=color, alpha=0.3, s=2, marker='x')

            total_inside += result['count_inside']
            total_points += result['total_points']

        ax1.set_xlim(-1.2, 1.2)
        ax1.set_ylim(-1.2, 1.2)
        ax1.set_aspect('equal')
        ax1.set_title(f'Multi-threaded Monte Carlo Simulation\nTotal Points: {total_points}')
        ax1.grid(True, alpha=0.3)
        
        # Top right: π estimation for each thread
        ax2 = axes[0, 1]
        thread_ids = [r['thread_id'] for r in thread_results]
        pi_estimates = [4 * r['count_inside'] / r['total_points'] for r in thread_results]

        bars = ax2.bar(thread_ids, pi_estimates,
                      color=[self.thread_colors[tid % len(self.thread_colors)] for tid in thread_ids],
                      alpha=0.7)
        ax2.axhline(y=np.pi, color='red', linestyle='--', linewidth=2, label='True π value')
        ax2.set_xlabel('Thread ID')
        ax2.set_ylabel('π Estimate')
        ax2.set_title('π Estimation Comparison by Thread')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # Bottom left: Cumulative π estimation (simulating real-time updates)
        ax3 = axes[1, 0]
        cumulative_inside = 0
        cumulative_total = 0
        cumulative_pi = []

        for result in sorted(thread_results, key=lambda x: x['thread_id']):
            cumulative_inside += result['count_inside']
            cumulative_total += result['total_points']
            cumulative_pi.append(4 * cumulative_inside / cumulative_total)

        ax3.plot(range(1, len(cumulative_pi) + 1), cumulative_pi,
                'bo-', linewidth=2, markersize=8, label='Cumulative π estimate')
        ax3.axhline(y=np.pi, color='red', linestyle='--', linewidth=2, label='True π value')
        ax3.set_xlabel('Number of Completed Threads')
        ax3.set_ylabel('Cumulative π Estimate')
        ax3.set_title('π Estimation Convergence Process')
        ax3.legend()
        ax3.grid(True, alpha=0.3)

        # Bottom right: Error analysis
        ax4 = axes[1, 1]
        final_pi_estimate = 4 * total_inside / total_points
        thread_errors = [abs(pi_est - np.pi) for pi_est in pi_estimates]
        final_error = abs(final_pi_estimate - np.pi)

        ax4.bar(thread_ids, thread_errors,
               color=[self.thread_colors[tid % len(self.thread_colors)] for tid in thread_ids],
               alpha=0.7, label='Thread errors')
        ax4.axhline(y=final_error, color='black', linestyle='-', linewidth=2,
                   label=f'Overall error: {final_error:.4f}')
        ax4.set_xlabel('Thread ID')
        ax4.set_ylabel('|Estimate - π|')
        ax4.set_title('Error Analysis')
        ax4.legend()
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('8/monte_carlo_visualization2.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        # Print statistical information
        print(f"\n=== Multi-threaded Monte Carlo π Estimation Results ===")
        print(f"Number of threads used: {self.n_threads}")
        print(f"Total points: {total_points}")
        print(f"Points inside circle: {total_inside}")
        print(f"π estimate: {final_pi_estimate:.6f}")
        print(f"True π value: {np.pi:.6f}")
        print(f"Absolute error: {final_error:.6f}")
        print(f"Relative error: {final_error/np.pi*100:.4f}%")

        return final_pi_estimate, final_error

# Run multi-threaded Monte Carlo simulation
if __name__ == "__main__":
    mc = MonteCarloPI(n_threads=4)
    results = mc.run_simulation(total_points=20000)
    pi_estimate, error = mc.visualize_results(results)

"""
问题：观察图像，如果将thread_colors数组修改为['blue', 'red', 'green', 'pink', 'purple', 'brown']，当前估算误差最小的柱子会变成什么颜色？

A. 红色
B. 绿色
C. 橙色
D. 保持蓝色不变

答案：A

推理过程：
1）观察右上角柱状图，当前蓝色柱子对应Thread 1（第二个柱子）；
2）代码第107-109行显示柱子颜色由thread_colors[tid % len(self.thread_colors)]决定；
3）原数组['red', 'blue', 'green', 'orange', 'purple', 'brown']中，Thread 1对应索引1，即'blue'；
4）修改后数组['blue', 'red', 'green', 'orange', 'purple', 'brown']中，Thread 1对应索引1，即'red'；
5）因此当前的蓝色柱子（Thread 1）会变成红色，必须结合图像观察当前柱子颜色和代码逻辑才能得出答案。
"""