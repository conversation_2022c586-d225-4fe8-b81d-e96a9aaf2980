<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Vibe Player</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      font-family: 'Segoe UI', sans-serif;
    }

    body {
      background: linear-gradient(135deg, #1a1a40, #0a0a2a);
      color: #fff;
      min-height: 100vh;
      display: flex;
      justify-content: center;
      align-items: center;
      transition: background 0.3s;
    }

    .player {
      background: rgba(255,255,255,0.05);
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
      border: 1px solid rgba(255,255,255,0.1);
      border-radius: 1rem;
      padding: 2rem;
      max-width: 1200px;
      width: 90%;
      display: grid;
      grid-template-columns: 1fr 2fr;
      gap: 2rem;
      box-shadow: 0 8px 32px rgba(0,0,0,0.3);
    }

    .left-panel {
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .vinyl-container {
      width: 200px;
      height: 200px;
      border-radius: 50%;
      background: #111;
      display: flex;
      justify-content: center;
      align-items: center;
      box-shadow: 0 0 20px rgba(0,0,0,0.5);
      position: relative;
      animation: rotate 10s linear infinite;
      animation-play-state: paused;
    }

    .vinyl-dot {
      width: 30px;
      height: 30px;
      border-radius: 50%;
      background: #6a0dad;
      box-shadow: 0 0 20px #6a0dad, 0 0 40px #6a0dad;
      animation: pulse 2s infinite;
    }

    .file-drop {
      margin-top: 2rem;
      border: 2px dashed rgba(255,255,255,0.3);
      border-radius: 0.5rem;
      padding: 1rem;
      text-align: center;
      cursor: pointer;
      transition: border-color 0.3s;
      width: 100%;
    }

    .file-drop:hover {
      border-color: rgba(255,255,255,0.5);
    }

    .right-panel {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }

    .top-buttons {
      display: flex;
      gap: 1rem;
      margin-bottom: 1rem;
    }

    .track-info {
      margin-bottom: 1rem;
    }

    .track-info h2 {
      font-size: 1.5rem;
    }

    .track-info p {
      opacity: 0.8;
    }

    .progress-control {
      margin-bottom: 1rem;
    }

    .time-display {
      margin-bottom: 0.5rem;
    }

    .controls {
      display: flex;
      flex-wrap: wrap;
      gap: 1rem;
      margin-bottom: 1.5rem;
    }

    .btn {
      background: linear-gradient(90deg, #6a0dad, #a239ca);
      border: none;
      padding: 0.5rem 1rem;
      border-radius: 0.3rem;
      color: #fff;
      cursor: pointer;
      transition: transform 0.2s, box-shadow 0.2s;
    }

    .btn:hover {
      transform: scale(1.05);
      box-shadow: 0 0 10px rgba(106,13,173,0.5);
    }

    .btn:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }

    .slider-container {
      display: flex;
      flex-direction: column;
      margin-bottom: 1rem;
    }

    .slider-container label {
      margin-bottom: 0.5rem;
    }

    input[type="range"] {
      -webkit-appearance: none;
      width: 100%;
      height: 4px;
      background: #444;
      border-radius: 2px;
      outline: none;
    }

    input[type="range"]::-webkit-slider-thumb {
      -webkit-appearance: none;
      width: 16px;
      height: 16px;
      background: #6a0dad;
      border-radius: 50%;
      cursor: pointer;
      transition: transform 0.2s;
    }

    input[type="range"]::-webkit-slider-thumb:hover {
      transform: scale(1.2);
    }

    .effect-panels {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1rem;
      margin-bottom: 1rem;
    }

    .panel {
      background: rgba(255,255,255,0.05);
      border: 1px solid rgba(255,255,255,0.1);
      border-radius: 0.5rem;
      padding: 1rem;
    }

    .url-input {
      display: flex;
      gap: 0.5rem;
      margin-bottom: 1rem;
    }

    .url-input input {
      flex: 1;
      padding: 0.5rem;
      border: 1px solid rgba(255,255,255,0.2);
      background: transparent;
      color: #fff;
      border-radius: 0.3rem;
    }

    .visuals {
      display: flex;
      gap: 1rem;
      margin-bottom: 1rem;
    }

    .spectrum-canvas {
      position: fixed;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 150px;
      z-index: -1;
    }

    @keyframes rotate {
      from { transform: rotate(0deg); }
      to { transform: rotate(360deg); }
    }

    @keyframes pulse {
      0% { 
        transform: scale(1); 
        box-shadow: 0 0 20px #6a0dad, 0 0 40px #6a0dad; 
      }
      50% { 
        transform: scale(1.1); 
        box-shadow: 0 0 30px #6a0dad, 0 0 50px #6a0dad; 
      }
      100% { 
        transform: scale(1); 
        box-shadow: 0 0 20px #6a0dad, 0 0 40px #6a0dad; 
      }
    }

    @media (max-width: 900px) {
      .player {
        grid-template-columns: 1fr;
      }
    }

    input[type="file"] {
      display: none;
    }
  </style>
</head>
<body>
  <canvas class="spectrum-canvas"></canvas>
  <div class="player">
    <div class="left-panel">
      <div class="vinyl-container" id="vinyl">
        <div class="vinyl-dot"></div>
      </div>
      <div class="file-drop" id="fileDrop">
        Click to select or drag & drop an audio file here
      </div>
      <input type="file" id="fileInput" accept="audio/*" />
    </div>

    <div class="right-panel">
      <div class="top-buttons">
        <button class="btn" id="loadDemoBtn">Load demo</button>
        <button class="btn" id="downloadBtn">Download current</button>
      </div>

      <div class="track-info" id="trackInfo">
        <h2>--</h2>
        <p>Ready • --</p>
      </div>

      <div class="progress-control">
        <div class="time-display" id="timeDisplay">0:00 / 0:00</div>
        <input type="range" id="progressBar" min="0" max="100" value="0" />
      </div>

      <div class="controls">
        <button class="btn" id="rewindBtn">-10s</button>
        <button class="btn" id="playPauseBtn">Play</button>
        <button class="btn" id="forwardBtn">+10s</button>
        <button class="btn" id="stopBtn">Stop</button>
        <button class="btn" id="muteBtn">Mute</button>
      </div>

      <div class="effect-panels">
        <div class="panel">
          <h3>Volume</h3>
          <div class="slider-container">
            <label>Volume: <span id="volumeValue">50</span></label>
            <input type="range" id="volumeSlider" min="0" max="1" step="0.01" value="0.5" />
          </div>
        </div>

        <div class="panel">
          <h3>Speed</h3>
          <div class="slider-container">
            <label>Speed: <span id="speedValue">1.00x</span></label>
            <input type="range" id="speedSlider" min="0.5" max="2" step="0.01" value="1" />
            <div class="speed-buttons">
              <button class="btn" data-speed="0.75">0.75x</button>
              <button class="btn" data-speed="1">1x</button>
              <button class="btn" data-speed="1.25">1.25x</button>
              <button class="btn" data-speed="1.5">1.5x</button>
            </div>
          </div>
        </div>

        <div class="panel">
          <h3>Echo</h3>
          <div class="slider-container">
            <label>Mix: <span id="echoMixValue">0</span></label>
            <input type="range" id="echoMixSlider" min="0" max="1" step="0.01" value="0" />
          </div>
          <div class="slider-container">
            <label>Time: <span id="echoTimeValue">0.35s</span></label>
            <input type="range" id="echoTimeSlider" min="0.1" max="1" step="0.01" value="0.35" />
          </div>
          <div class="slider-container">
            <label>Feedback: <span id="echoFeedbackValue">0.30</span></label>
            <input type="range" id="echoFeedbackSlider" min="0" max="0.9" step="0.01" value="0.3" />
          </div>
        </div>

        <div class="panel">
          <h3>Visuals & Tone</h3>
          <div class="visuals">
            <button class="btn" id="eqBarsBtn">Equalizer Bars</button>
            <button class="btn" id="particlesBtn">Particles</button>
            <button class="btn" id="glowPulseBtn">Glow Pulse</button>
          </div>
          <div class="url-input">
            <input type="text" id="audioUrlInput" placeholder="Paste audio URL" />
            <button class="btn" id="loadUrlBtn">Load</button>
          </div>
        </div>

        <div class="panel">
          <h3>Bass Boost</h3>
          <div class="slider-container">
            <label>Gain: <span id="bassGainValue">0.0 dB</span></label>
            <input type="range" id="bassGainSlider" min="-10" max="10" step="0.1" value="0" />
          </div>
        </div>

        <div class="panel">
          <h3>Colors</h3>
          <div>
            <input type="checkbox" id="autoHues" checked />
            <label for="autoHues">Auto hues</label>
          </div>
          <div class="slider-container">
            <label>Tint</label>
            <input type="color" id="tintColor" value="#6a0dad" />
          </div>
        </div>
      </div>

      <div class="shortcuts">
        <p>Space = Play/Pause • ←/→ = Seek • ↑/↓ = Volume</p>
      </div>
    </div>
  </div>

  <script>
    // 元素初始化
    const audio = new Audio();
    const fileDrop = document.getElementById('fileDrop');
    const fileInput = document.getElementById('fileInput');
    const trackInfo = document.getElementById('trackInfo');
    const trackTitle = trackInfo.querySelector('h2');
    const trackStatus = trackInfo.querySelector('p');
    const progressBar = document.getElementById('progressBar');
    const timeDisplay = document.getElementById('timeDisplay');
    const playPauseBtn = document.getElementById('playPauseBtn');
    const rewindBtn = document.getElementById('rewindBtn');
    const forwardBtn = document.getElementById('forwardBtn');
    const stopBtn = document.getElementById('stopBtn');
    const muteBtn = document.getElementById('muteBtn');
    const volumeSlider = document.getElementById('volumeSlider');
    const volumeValue = document.getElementById('volumeValue');
    const speedSlider = document.getElementById('speedSlider');
    const speedValue = document.getElementById('speedValue');
    const speedButtons = document.querySelectorAll('.speed-buttons .btn');
    const echoMixSlider = document.getElementById('echoMixSlider');
    const echoMixValue = document.getElementById('echoMixValue');
    const echoTimeSlider = document.getElementById('echoTimeSlider');
    const echoTimeValue = document.getElementById('echoTimeValue');
    const echoFeedbackSlider = document.getElementById('echoFeedbackSlider');
    const echoFeedbackValue = document.getElementById('echoFeedbackValue');
    const audioUrlInput = document.getElementById('audioUrlInput');
    const loadUrlBtn = document.getElementById('loadUrlBtn');
    const loadDemoBtn = document.getElementById('loadDemoBtn');
    const downloadBtn = document.getElementById('downloadBtn');
    const vinyl = document.getElementById('vinyl');
    const spectrumCanvas = document.querySelector('.spectrum-canvas');
    const ctx = spectrumCanvas.getContext('2d');
    const bassGainSlider = document.getElementById('bassGainSlider');
    const bassGainValue = document.getElementById('bassGainValue');
    const autoHues = document.getElementById('autoHues');
    const tintColor = document.getElementById('tintColor');

    // Web Audio 初始化（频谱 + 回声）
    let audioCtx, analyser, source, delayNode, feedbackGain, mixGain;
    function initAudioContext() {
      if (!audioCtx) {
        audioCtx = new (window.AudioContext || window.webkitAudioContext)();
        analyser = audioCtx.createAnalyser();
        analyser.fftSize = 256;
        const bufferLength = analyser.frequencyBinCount;
        const dataArray = new Uint8Array(bufferLength);

        // 回声节点配置
        delayNode = audioCtx.createDelay(1);
        feedbackGain = audioCtx.createGain();
        mixGain = audioCtx.createGain();

        // 音频路由：干声 + 湿声（回声）
        source = audioCtx.createMediaElementSource(audio);
        source.connect(mixGain); // 干声直接输出
        
        source.connect(delayNode);
        delayNode.connect(feedbackGain);
        feedbackGain.connect(delayNode); // 反馈循环
        delayNode.connect(mixGain); // 湿声输出到混合节点
        
        mixGain.connect(analyser);
        analyser.connect(audioCtx.destination);

        // 实时绘制频谱
        function drawSpectrum() {
          requestAnimationFrame(drawSpectrum);
          analyser.getByteFrequencyData(dataArray);
          ctx.clearRect(0, 0, spectrumCanvas.width,