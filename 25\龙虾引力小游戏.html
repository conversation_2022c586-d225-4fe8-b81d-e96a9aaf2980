<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>宇宙小游戏 - Canvas Gravity Sandbox</title>
  <style>
    :root {
      --bg-deep: #0b1020;
      --bg-mid: #111a30;
      --bg-soft: #141e35;
      --panel: rgba(20, 30, 53, 0.75);
      --panel-border: rgba(255, 255, 255, 0.08);
      --text: #e5ecff;
      --text-dim: #a9b6d9;
      --accent: #6aa8ff;
      --accent-2: #a679ff;
      --good: #53dfa7;
      --warn: #ffb86b;
      --danger: #ff6b6b;
      --shadow-strong: rgba(0,0,0,0.45);
      --shadow-soft: rgba(0,0,0,0.25);
    }

    html, body {
      height: 100%;
      margin: 0;
      background: radial-gradient(circle at 50% 35%, var(--bg-mid), var(--bg-deep) 65%);
      color: var(--text);
      font-family: ui-sans-serif, system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial, Noto Sans, "Apple Color Emoji", "Segoe UI Emoji";
      overflow: hidden;
      user-select: none;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }

    #app {
      position: relative;
      width: 100%;
      height: 100%;
    }

    canvas {
      position: absolute;
      inset: 0;
      width: 100%;
      height: 100%;
      display: block;
      background: radial-gradient(1200px at 55% 40%, rgba(106, 168, 255, 0.08), transparent 70%),
                  radial-gradient(1000px at 30% 70%, rgba(166, 121, 255, 0.08), transparent 70%);
      cursor: grab;
    }
    canvas.dragging {
      cursor: grabbing;
    }

    .panel {
      position: absolute;
      top: 16px;
      left: 16px;
      width: 340px;
      max-height: calc(100% - 32px);
      overflow: auto;
      background: var(--panel);
      border: 1px solid var(--panel-border);
      border-radius: 16px;
      backdrop-filter: blur(10px) saturate(130%);
      box-shadow: 0 10px 30px var(--shadow-soft), inset 0 0 0 1px rgba(255,255,255,0.03);
      padding: 16px 18px 18px 18px;
    }

    .panel h1 {
      font-size: 18px;
      margin: 0 0 12px;
      letter-spacing: 0.2px;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .subtitle {
      color: var(--text-dim);
      font-size: 12px;
      margin-bottom: 16px;
      line-height: 1.5;
    }

    .row {
      display: grid;
      grid-template-columns: 1fr auto;
      align-items: center;
      gap: 10px;
      margin-bottom: 14px;
    }

    .row label {
      font-size: 13px;
      color: var(--text);
    }

    .value {
      font-variant-numeric: tabular-nums;
      color: var(--accent);
      font-size: 12px;
    }

    .slider-wrap {
      grid-column: 1 / -1;
      margin-top: -2px;
    }

    input[type="range"] {
      width: 100%;
      -webkit-appearance: none;
      background: transparent;
      height: 24px;
    }
    input[type="range"]:focus { outline: none; }
    input[type="range"]::-webkit-slider-runnable-track {
      height: 6px;
      background: linear-gradient(90deg, rgba(106,168,255,0.6), rgba(166,121,255,0.6));
      border-radius: 999px;
      box-shadow: inset 0 0 0 1px rgba(255,255,255,0.12);
    }
    input[type="range"]::-webkit-slider-thumb {
      -webkit-appearance: none;
      margin-top: -6px;
      width: 18px; height: 18px;
      border-radius: 50%;
      background: linear-gradient(180deg, #ffffff, #dbe8ff);
      border: 1px solid rgba(0,0,0,0.15);
      box-shadow: 0 4px 10px rgba(0,0,0,0.25), 0 0 0 4px rgba(106,168,255,0.25);
    }
    input[type="range"]::-moz-range-track {
      height: 6px;
      background: linear-gradient(90deg, rgba(106,168,255,0.6), rgba(166,121,255,0.6));
      border-radius: 999px;
      box-shadow: inset 0 0 0 1px rgba(255,255,255,0.12);
    }
    input[type="range"]::-moz-range-thumb {
      width: 18px; height: 18px; border: none; border-radius: 50%;
      background: linear-gradient(180deg, #ffffff, #dbe8ff);
      box-shadow: 0 4px 10px rgba(0,0,0,0.25), 0 0 0 4px rgba(106,168,255,0.25);
    }

    .switch {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-bottom: 12px;
      font-size: 13px;
    }
    .switch input { display: none; }
    .toggle {
      width: 42px; height: 24px; border-radius: 999px;
      background: rgba(255,255,255,0.15);
      border: 1px solid var(--panel-border);
      position: relative; cursor: pointer;
      transition: background 0.2s ease;
    }
    .toggle::after {
      content: "";
      position: absolute; top: 50%; left: 4px; transform: translateY(-50%);
      width: 18px; height: 18px; border-radius: 999px;
      background: linear-gradient(180deg, #ffffff, #dbe8ff);
      border: 1px solid rgba(0,0,0,0.15);
      transition: left 0.2s ease;
      box-shadow: 0 3px 8px rgba(0,0,0,0.25);
    }
    .switch input:checked + .toggle {
      background: linear-gradient(90deg, rgba(106,168,255,0.6), rgba(166,121,255,0.6));
    }
    .switch input:checked + .toggle::after { left: 20px; }

    .buttons {
      display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin-top: 8px; margin-bottom: 6px;
    }
    .btn {
      appearance: none; -webkit-appearance: none; cursor: pointer;
      border: 1px solid var(--panel-border);
      background: linear-gradient(180deg, rgba(255,255,255,0.08), rgba(255,255,255,0.02));
      color: var(--text);
      border-radius: 12px; padding: 10px 12px; font-size: 13px; text-align: center;
      transition: transform 0.06s ease, background 0.2s ease, border-color 0.2s ease;
      box-shadow: 0 6px 16px var(--shadow-soft);
    }
    .btn:hover { border-color: rgba(255,255,255,0.22); }
    .btn:active { transform: translateY(1px) scale(0.99); }
    .btn.primary { background: linear-gradient(90deg, rgba(106,168,255,0.8), rgba(166,121,255,0.8)); border: none; }
    .btn.danger { background: linear-gradient(180deg, rgba(255,107,107,0.85), rgba(255,107,107,0.65)); border: none; }

    .metrics {
      display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin-top: 14px;
    }
    .metric {
      background: rgba(255,255,255,0.04);
      border: 1px solid var(--panel-border);
      border-radius: 12px; padding: 10px 12px;
      box-shadow: inset 0 0 0 1px rgba(255,255,255,0.02);
    }
    .metric .label { color: var(--text-dim); font-size: 11px; margin-bottom: 4px; }
    .metric .num { font-size: 16px; color: var(--text); font-variant-numeric: tabular-nums; }

    .help {
      position: absolute; right: 16px; top: 16px;
      background: var(--panel);
      border: 1px solid var(--panel-border);
      border-radius: 16px; padding: 14px 16px; width: 320px; max-width: calc(100% - 32px);
      backdrop-filter: blur(10px) saturate(130%);
      box-shadow: 0 10px 30px var(--shadow-soft), inset 0 0 0 1px rgba(255,255,255,0.03);
      color: var(--text-dim); font-size: 13px; line-height: 1.6;
    }
    .help strong { color: var(--text); }
    .kbd {
      display: inline-block; border-radius: 8px; padding: 2px 8px; border: 1px solid var(--panel-border);
      background: rgba(255,255,255,0.06); color: var(--text); font-weight: 600; font-size: 12px; margin: 0 2px;
      box-shadow: inset 0 0 0 1px rgba(255,255,255,0.04);
    }

    .hint {
      position: absolute; left: 50%; bottom: 16px; transform: translateX(-50%);
      background: rgba(0,0,0,0.35);
      color: #dbe8ff; font-size: 13px; padding: 8px 14px; border-radius: 999px;
      border: 1px solid rgba(255,255,255,0.08);
      backdrop-filter: blur(6px);
      box-shadow: 0 10px 24px var(--shadow-soft);
      pointer-events: none;
    }

    .badge {
      display: inline-flex; align-items: center; gap: 6px;
      font-size: 11px; color: var(--good);
      background: rgba(83,223,167,0.1); border: 1px solid rgba(83,223,167,0.2);
      border-radius: 999px; padding: 2px 8px; margin-left: 4px;
    }
  </style>
</head>
<body>
  <div id="app">
    <canvas id="canvas"></canvas>

    <section class="panel" id="panel" tabindex="0">
      <h1>
        宇宙小游戏
        <span class="badge" title="基于 Canvas 2D">Canvas</span>
      </h1>
      <div class="subtitle">
        左键拖拽移动、滚轮缩放，<strong>右键按住拖拽</strong>放置天体并设定速度方向。行星会因引力相互吸引，显示轨迹拖尾。
      </div>

      <div class="row">
        <label for="gravity">引力常数 G</label>
        <div class="value" id="gravityVal">0.100</div>
        <div class="slider-wrap">
          <input id="gravity" type="range" min="0" max="100" step="0.001" value="0.1" />
        </div>
      </div>

      <div class="row">
        <label for="timeScale">时间倍率</label>
        <div class="value" id="timeVal">1.00x</div>
        <div class="slider-wrap">
          <input id="timeScale" type="range" min="0.1" max="3" step="0.01" value="1" />
        </div>
      </div>

      <div class="row">
        <label for="speedFactor">速度因子</label>
        <div class="value" id="speedVal">1.0</div>
        <div class="slider-wrap">
          <input id="speedFactor" type="range" min="0" max="5" step="0.05" value="1" />
        </div>
      </div>

      <div class="row">
        <label for="trailLen">拖尾长度</label>
        <div class="value" id="trailVal">300</div>
        <div class="slider-wrap">
          <input id="trailLen" type="range" min="10" max="1000" step="10" value="300" />
        </div>
      </div>

      <div class="row">
        <label for="newMass">新天体质量</label>
        <div class="value" id="massVal">50</div>
        <div class="slider-wrap">
          <input id="newMass" type="range" min="1" max="500" step="1" value="50" />
        </div>
      </div>

      <div class="row">
        <label for="sizeFactor">尺寸因子</label>
        <div class="value" id="sizeVal">1.00</div>
        <div class="slider-wrap">
          <input id="sizeFactor" type="range" min="0.5" max="3" step="0.01" value="1" />
        </div>
      </div>

      <label class="switch" title="发生碰撞时合并质量与动量">
        <input id="merge" type="checkbox" checked />
        <span class="toggle"></span>
        合并碰撞
      </label>

      <label class="switch" title="显示速度向量">
        <input id="showVec" type="checkbox" />
        <span class="toggle"></span>
        显示速度向量
      </label>

      <div class="buttons">
        <button class="btn primary" id="pauseBtn">暂停</button>
        <button class="btn" id="resetCamBtn">重置视角</button>
        <button class="btn" id="demoBtn">添加示例系统</button>
        <button class="btn danger" id="clearBtn">清空</button>
      </div>

      <div class="metrics">
        <div class="metric">
          <div class="label">天体数量</div>
          <div class="num" id="count">0</div>
        </div>
        <div class="metric">
          <div class="label">FPS</div>
          <div class="num" id="fps">0</div>
        </div>
      </div>
    </section>

    <section class="help">
      <strong>交互说明</strong><br />
      • <span class="kbd">左键</span> 按住拖拽：移动视角（平移）<br />
      • <span class="kbd">滚轮</span>：缩放（支持以鼠标指向处为中心缩放）<br />
      • <span class="kbd">右键</span> 按住并拖拽：放置天体，拖拽方向即初速度方向，长度为速度大小。<br />
      • <span class="kbd">双击</span> 画面：以该点为中心重置到合适缩放。<br />
      • 面板可调：引力常数、时间倍率、速度因子、拖尾长度等。
    </section>

    <div class="hint">右键按住拖拽即可放置新天体</div>
  </div>

  <script>
    const canvas = document.getElementById('canvas');
    const ctx = canvas.getContext('2d');

    // UI elements
    const gravityEl = document.getElementById('gravity');
    const gravityVal = document.getElementById('gravityVal');
    const timeScaleEl = document.getElementById('timeScale');
    const timeVal = document.getElementById('timeVal');
    const speedFactorEl = document.getElementById('speedFactor');
    const speedVal = document.getElementById('speedVal');
    const trailLenEl = document.getElementById('trailLen');
    const trailVal = document.getElementById('trailVal');
    const newMassEl = document.getElementById('newMass');
    const massVal = document.getElementById('massVal');
    const sizeFactorEl = document.getElementById('sizeFactor');
    const sizeVal = document.getElementById('sizeVal');
    const mergeEl = document.getElementById('merge');
    const showVecEl = document.getElementById('showVec');

    const pauseBtn = document.getElementById('pauseBtn');
    const resetCamBtn = document.getElementById('resetCamBtn');
    const clearBtn = document.getElementById('clearBtn');
    const demoBtn = document.getElementById('demoBtn');

    const countEl = document.getElementById('count');
    const fpsEl = document.getElementById('fps');

    let W = 0, H = 0, DPR = Math.max(1, Math.min(window.devicePixelRatio || 1, 2));
    function resize() {
      W = canvas.clientWidth; H = canvas.clientHeight;
      canvas.width = Math.floor(W * DPR); canvas.height = Math.floor(H * DPR);
      ctx.setTransform(DPR, 0, 0, DPR, 0, 0); // draw in CSS pixels
    }
    window.addEventListener('resize', resize);
    resize();

    // Camera
    const camera = {
      x: 0, y: 0, // world coords at screen center
      scale: 1,
    };

    function worldToScreen(wx, wy) {
      return [ (wx - camera.x) * camera.scale + W/2, (wy - camera.y) * camera.scale + H/2 ];
    }
    function screenToWorld(sx, sy) {
      return [ (sx - W/2) / camera.scale + camera.x, (sy - H/2) / camera.scale + camera.y ];
    }

    function zoomAt(factor, sx, sy) {
      const [wx, wy] = screenToWorld(sx, sy);
      const newScale = camera.scale * factor;
      camera.x = wx - (sx - W/2) / newScale;
      camera.y = wy - (sy - H/2) / newScale;
      camera.scale = newScale;
    }

    // Physics parameters
    let G = parseFloat(gravityEl.value); // gravitational constant
    let timeScale = parseFloat(timeScaleEl.value);
    let speedFactor = parseFloat(speedFactorEl.value);
    let trailLimit = parseInt(trailLenEl.value, 10);
    let newMass = parseFloat(newMassEl.value);
    let sizeFactor = parseFloat(sizeFactorEl.value);
    const softening = 0.5; // epsilon^2 to avoid singularity

    const bodies = [];
    let paused = false;

    // Starfield in world space
    const stars = [];
    function generateStars(n = 800) {
      stars.length = 0;
      const RANGE = 20000; // world units
      for (let i = 0; i < n; i++) {
        const x = (Math.random() * 2 - 1) * RANGE;
        const y = (Math.random() * 2 - 1) * RANGE;
        const r = Math.random() < 0.7 ? 1 : 2;
        const a = 0.7 + Math.random() * 0.3;
        const hue = 200 + Math.random() * 60; // bluish white
        stars.push({ x, y, r, a, hue });
      }
    }
    generateStars();

    function createBody({ x, y, vx = 0, vy = 0, mass = 10, color }) {
      const hue = color ?? (Math.random() * 360);
      const body = {
        x, y, vx, vy, ax: 0, ay: 0,
        mass,
        hue,
        trail: [],
      };
      bodies.push(body);
      return body;
    }

    function radiusOf(m) {
      return Math.max(1.5, sizeFactor * Math.cbrt(m));
    }

    // UI updates
    function fmt(n, digits = 3) { return parseFloat(n).toFixed(digits); }
    function syncUI() {
      gravityVal.textContent = fmt(G, 3);
      timeVal.textContent = fmt(timeScale, 2) + 'x';
      speedVal.textContent = fmt(speedFactor, 1);
      trailVal.textContent = String(trailLimit);
      massVal.textContent = String(Math.round(newMass));
      sizeVal.textContent = fmt(sizeFactor, 2);
      countEl.textContent = String(bodies.length);
    }
    syncUI();

    gravityEl.addEventListener('input', () => { G = parseFloat(gravityEl.value); syncUI(); });
    timeScaleEl.addEventListener('input', () => { timeScale = parseFloat(timeScaleEl.value); syncUI(); });
    speedFactorEl.addEventListener('input', () => { speedFactor = parseFloat(speedFactorEl.value); syncUI(); });
    trailLenEl.addEventListener('input', () => { trailLimit = parseInt(trailLenEl.value, 10); syncUI(); });
    newMassEl.addEventListener('input', () => { newMass = parseFloat(newMassEl.value); syncUI(); });
    sizeFactorEl.addEventListener('input', () => { sizeFactor = parseFloat(sizeFactorEl.value); syncUI(); });

    pauseBtn.addEventListener('click', () => {
      paused = !paused; pauseBtn.textContent = paused ? '继续' : '暂停';
    });

    resetCamBtn.addEventListener('click', () => {
      camera.x = 0; camera.y = 0; camera.scale = 1;
    });

    clearBtn.addEventListener('click', () => {
      bodies.length = 0; syncUI();
    });

    demoBtn.addEventListener('click', () => {
      addDemoSystem();
    });

    // Demo system: a star with planets
    function addDemoSystem() {
      const cx = camera.x, cy = camera.y;
      const star = createBody({ x: cx, y: cy, mass: 1000, color: 50 });
      // three planets
      const planets = [
        { r: 120, mass: 10, hue: 200 },
        { r: 220, mass: 18, hue: 280 },
        { r: 320, mass: 26, hue: 15 },
      ];
      for (const p of planets) {
        const angle = Math.random() * Math.PI * 2;
        const x = cx + Math.cos(angle) * p.r;
        const y = cy + Math.sin(angle) * p.r;
        // circular orbit speed v = sqrt(G*M/r)
        const v = Math.sqrt(G * star.mass / p.r);
        const dirx = -Math.sin(angle), diry = Math.cos(angle);
        createBody({ x, y, vx: dirx * v, vy: diry * v, mass: p.mass, color: p.hue });
      }
      syncUI();
    }

    // Interaction state
    const state = {
      leftDragging: false,
      rightPlacing: false,
      lastX: 0, lastY: 0,
      placeStartWorld: { x: 0, y: 0 },
      placeCurWorld: { x: 0, y: 0 },
    };

    canvas.addEventListener('contextmenu', (e) => e.preventDefault());

    canvas.addEventListener('mousedown', (e) => {
      const rect = canvas.getBoundingClientRect();
      const sx = e.clientX - rect.left; const sy = e.clientY - rect.top;
      state.lastX = sx; state.lastY = sy;

      if (e.button === 0) {
        state.leftDragging = true; canvas.classList.add('dragging');
      } else if (e.button === 2) {
        state.rightPlacing = true;
        const [wx, wy] = screenToWorld(sx, sy);
        state.placeStartWorld.x = wx; state.placeStartWorld.y = wy;
        state.placeCurWorld.x = wx; state.placeCurWorld.y = wy;
      }
    });

    window.addEventListener('mousemove', (e) => {
      const rect = canvas.getBoundingClientRect();
      const sx = e.clientX - rect.left; const sy = e.clientY - rect.top;
      const dx = sx - state.lastX; const dy = sy - state.lastY;
      state.lastX = sx; state.lastY = sy;

      if (state.leftDragging) {
        camera.x -= dx / camera.scale;
        camera.y -= dy / camera.scale;
      }

      if (state.rightPlacing) {
        const [wx, wy] = screenToWorld(sx, sy);
        state.placeCurWorld.x = wx; state.placeCurWorld.y = wy;
      }
    });

    window.addEventListener('mouseup', (e) => {
      if (e.button === 0) {
        state.leftDragging = false; canvas.classList.remove('dragging');
      }
      if (e.button === 2 && state.rightPlacing) {
        // Create body with velocity based on drag vector
        const sx = state.placeStartWorld.x;
        const sy = state.placeStartWorld.y;
        const ex = state.placeCurWorld.x;
        const ey = state.placeCurWorld.y;
        const vx = (ex - sx) * speedFactor;
        const vy = (ey - sy) * speedFactor;
        createBody({ x: sx, y: sy, vx, vy, mass: newMass });
        syncUI();
        state.rightPlacing = false;
      }
    });

    canvas.addEventListener('wheel', (e) => {
      const rect = canvas.getBoundingClientRect();
      const sx = e.clientX - rect.left; const sy = e.clientY - rect.top;
      const delta = -e.deltaY; // up: positive
      const factor = delta > 0 ? 1.1 : 1/1.1;
      zoomAt(factor, sx, sy);
      e.preventDefault();
    }, { passive: false });

    canvas.addEventListener('dblclick', (e) => {
      const rect = canvas.getBoundingClientRect();
      const sx = e.clientX - rect.left; const sy = e.clientY - rect.top;
      const [wx, wy] = screenToWorld(sx, sy);
      camera.x = wx; camera.y = wy; camera.scale = 1.2;
    });

    // Physics update
    function step(dt) {
      // dt in seconds
      if (bodies.length === 0) return;

      // reset acceleration
      for (let i = 0; i < bodies.length; i++) {
        const b = bodies[i]; b.ax = 0; b.ay = 0;
      }

      // compute forces O(n^2)
      for (let i = 0; i < bodies.length; i++) {
        const bi = bodies[i];
        for (let j = i + 1; j < bodies.length; j++) {
          const bj = bodies[j];
          let dx = bj.x - bi.x; let dy = bj.y - bi.y;
          const dist2 = dx*dx + dy*dy + softening*softening;
          const dist = Math.sqrt(dist2);
          const invDist3 = 1 / (dist2 * dist);
          const force = G * bi.mass * bj.mass * invDist3; // multiply by (dx, dy)
          const fx = force * dx; const fy = force * dy;
          bi.ax += fx / bi.mass; bi.ay += fy / bi.mass;
          bj.ax -= fx / bj.mass; bj.ay -= fy / bj.mass;
        }
      }

      // integrate
      for (let i = 0; i < bodies.length; i++) {
        const b = bodies[i];
        b.vx += b.ax * dt; b.vy += b.ay * dt;
        b.x += b.vx * dt; b.y += b.vy * dt;
        // trail
        b.trail.push({ x: b.x, y: b.y });
        if (b.trail.length > trailLimit) b.trail.shift();
      }

      // collisions merge
      if (mergeEl.checked) {
        // naive pair checking; if merged, mark and remove later
        const alive = new Array(bodies.length).fill(true);
        for (let i = 0; i < bodies.length; i++) {
          if (!alive[i]) continue;
          for (let j = i + 1; j < bodies.length; j++) {
            if (!alive[j]) continue;
            const bi = bodies[i], bj = bodies[j];
            const dx = bj.x - bi.x, dy = bj.y - bi.y;
            const dist = Math.hypot(dx, dy);
            const rsum = radiusOf(bi.mass) + radiusOf(bj.mass);
            if (dist < rsum * 0.8) { // merge threshold a bit smaller to avoid early merge when zoom changes
              const mt = bi.mass + bj.mass;
              const nx = (bi.x * bi.mass + bj.x * bj.mass) / mt;
              const ny = (bi.y * bi.mass + bj.y * bj.mass) / mt;
              const nvx = (bi.vx * bi.mass + bj.vx * bj.mass) / mt;
              const nvy = (bi.vy * bi.mass + bj.vy * bj.mass) / mt;
              const nhue = (bi.hue * bi.mass + bj.hue * bj.mass) / mt;
              const merged = createBody({ x: nx, y: ny, vx: nvx, vy: nvy, mass: mt, color: nhue });
              // merge trails (keep the longer one)
              merged.trail = (bi.trail.length > bj.trail.length) ? bi.trail.slice(-trailLimit) : bj.trail.slice(-trailLimit);
              alive[i] = false; alive[j] = false;
              // mark both to be removed; and push merged to temp list
            }
          }
        }
        // rebuild body list (keep alive and newly created at end already inserted)
        const mergedAdded = bodies.slice(); // includes new merges at tail
        bodies.length = 0;
        for (let k = 0; k < alive.length; k++) {
          if (alive[k]) bodies.push(mergedAdded[k]);
        }
        // extra entries after alive.length are newly created merged bodies
        for (let k = alive.length; k < mergedAdded.length; k++) bodies.push(mergedAdded[k]);
      }
    }

    // Rendering
    function drawStarfield() {
      ctx.save();
      for (const s of stars) {
        const [sx, sy] = worldToScreen(s.x, s.y);
        if (sx < -20 || sy < -20 || sx > W + 20 || sy > H + 20) continue;
        ctx.beginPath();
        ctx.fillStyle = `hsla(${s.hue}, 80%, 90%, ${s.a})`;
        ctx.arc(sx, sy, s.r, 0, Math.PI * 2);
        ctx.fill();
      }
      ctx.restore();
    }

    function drawTrails() {
      ctx.save();
      ctx.lineWidth = Math.max(1, 1.2 * Math.min(1.5, camera.scale));
      ctx.lineCap = 'round';
      for (const b of bodies) {
        if (b.trail.length < 2) continue;
        const n = b.trail.length;
        for (let i = 1; i < n; i++) {
          const p0 = b.trail[i - 1];
          const p1 = b.trail[i];
          const [sx0, sy0] = worldToScreen(p0.x, p0.y);
          const [sx1, sy1] = worldToScreen(p1.x, p1.y);
          const t = i / n;
          const alpha = t * 0.8; // fade in newer segments
          ctx.strokeStyle = `hsla(${b.hue}, 80%, 60%, ${alpha})`;
          ctx.beginPath();
          ctx.moveTo(sx0, sy0);
          ctx.lineTo(sx1, sy1);
          ctx.stroke();
        }
      }
      ctx.restore();
    }

    function drawBodies() {
      for (const b of bodies) {
        const [sx, sy] = worldToScreen(b.x, b.y);
        const r = radiusOf(b.mass) * camera.scale; // physical size scales with zoom

        // Glow
        ctx.save();
        ctx.beginPath();
        const grad = ctx.createRadialGradient(sx, sy, 0, sx, sy, r * 3);
        grad.addColorStop(0, `hsla(${b.hue}, 90%, 70%, 0.65)`);
        grad.addColorStop(1, 'rgba(0,0,0,0)');
        ctx.fillStyle = grad;
        ctx.globalCompositeOperation = 'lighter';
        ctx.arc(sx, sy, r * 3, 0, Math.PI * 2);
        ctx.fill();
        ctx.restore();

        // Core
        ctx.save();
        ctx.beginPath();
        const core = ctx.createRadialGradient(sx, sy, 0, sx, sy, r);
        core.addColorStop(0, `hsla(${b.hue}, 90%, 95%, 1)`);
        core.addColorStop(0.6, `hsla(${b.hue}, 85%, 60%, 0.95)`);
        core.addColorStop(1, `hsla(${b.hue}, 85%, 45%, 0.95)`);
        ctx.fillStyle = core;
        ctx.shadowColor = 'rgba(0,0,0,0.6)';
        ctx.shadowBlur = 8;
        ctx.arc(sx, sy, r, 0, Math.PI * 2);
        ctx.fill();
        ctx.restore();

        // velocity vector
        if (showVecEl.checked) {
          ctx.save();
          const scale = 8 * Math.sqrt(camera.scale); // visual scale for velocity arrow
          const vx = b.vx * scale; const vy = b.vy * scale;
          ctx.strokeStyle = `hsla(${b.hue}, 90%, 70%, 0.9)`;
          ctx.lineWidth = 2;
          drawArrow(sx, sy, sx + vx, sy + vy);
          ctx.restore();
        }
      }
    }

    function drawPlacing() {
      if (!state.rightPlacing) return;
      const s = state.placeStartWorld; const c = state.placeCurWorld;
      const [sx, sy] = worldToScreen(s.x, s.y);
      const [ex, ey] = worldToScreen(c.x, c.y);
      const dx = c.x - s.x; const dy = c.y - s.y;
      const speed = Math.hypot(dx, dy) * speedFactor;

      ctx.save();
      ctx.lineWidth = 2;
      ctx.strokeStyle = 'rgba(255,255,255,0.9)';
      drawArrow(sx, sy, ex, ey);

      // speed label
      const label = `v = ${speed.toFixed(2)}`;
      ctx.font = '12px system-ui, sans-serif';
      ctx.fillStyle = 'rgba(0,0,0,0.55)';
      ctx.fillRect(ex + 8, ey - 16, ctx.measureText(label).width + 12, 20);
      ctx.fillStyle = 'rgba(255,255,255,0.95)';
      ctx.fillText(label, ex + 14, ey - 2);
      ctx.restore();
    }

    function drawArrow(x0, y0, x1, y1) {
      const dx = x1 - x0; const dy = y1 - y0;
      const len = Math.hypot(dx, dy);
      if (len < 1e-6) return;
      const ux = dx / len, uy = dy / len;
      const head = Math.min(16, Math.max(10, len * 0.15));
      ctx.beginPath();
      ctx.moveTo(x0, y0);
      ctx.lineTo(x1, y1);
      ctx.stroke();
      ctx.beginPath();
      ctx.moveTo(x1, y1);
      ctx.lineTo(x1 - ux * head + -uy * head * 0.35, y1 - uy * head + ux * head * 0.35);
      ctx.lineTo(x1 - ux * head + uy * head * 0.35, y1 - uy * head + -ux * head * 0.35);
      ctx.closePath();
      ctx.fillStyle = ctx.strokeStyle;
      ctx.fill();
    }

    // FPS
    let lastTime = performance.now();
    let accum = 0; // seconds accumulator
    let fpsCounter = 0; let fpsValue = 0; let lastFpsUpdate = 0;

    function loop(now) {
      const dtMs = now - lastTime; lastTime = now;
      let dt = Math.min(0.05, dtMs / 1000) * timeScale; // clamp for stability
      if (!paused) step(dt);

      // FPS measure
      fpsCounter++;
      if (now - lastFpsUpdate > 500) {
        fpsValue = Math.round((fpsCounter * 1000) / (now - lastFpsUpdate));
        fpsCounter = 0; lastFpsUpdate = now; fpsEl.textContent = String(fpsValue);
      }

      // Draw
      ctx.clearRect(0, 0, W, H);
      drawStarfield();
      drawTrails();
      drawBodies();
      drawPlacing();

      requestAnimationFrame(loop);
    }
    requestAnimationFrame(loop);

    // Start with a demo
    addDemoSystem();
    syncUI();
  </script>
</body>
</html>