## 客观题

| 图片                    | 问题                                                         |
| ----------------------- | ------------------------------------------------------------ |
| ![](images/image.png)   | AB两图中哪个图是通过以下代码生成的？ Python 版本 3.11.0, matplotlib 版本 3.6.3, seaborn 版本 0.12.2 import matplotlib.pyplot as pltimport numpy as npnp.random.seed(0)x = np.random.rand(1000)y = np.random.rand(1000) \* 2 - 1labels = np.random.choice(\[0, 1], size=1000)plt.figure(figsize=(8, 8))plt.scatter(x\[labels == 0], y\[labels == 0], c='blue', label='False', alpha=0.6)plt.scatter(x\[labels == 1], y\[labels == 1], c='red', label='True', alpha=0.6)plt.axhline(0.5, color='black', linestyle=':', linewidth=1)plt.axvline(0, color='black', linestyle=':', linewidth=1)plt.xlabel(r'$P(F\_{i+1} \| F\_i)$')plt.ylabel(r'$P(F\_{i+2} \| F\_{i+1}) - P(F\_{i+1} \| F\_i)$')plt.legend(title='Boundary')plt.hist(x\[labels == 0], bins=30, color='blue', alpha=0.3, orientation='vertical', density=True)plt.hist(x\[labels == 1], bins=30, color='red', alpha=0.3, orientation='vertical', density=True)plt.hist(y\[labels == 0], bins=30, color='blue', alpha=0.3, orientation='horizontal', density=True)plt.hist(y\[labels == 1], bins=30, color='red', alpha=0.3, orientation='horizontal', density=True)plt.text(-0.1, -1, r'$\alpha\_1$', fontsize=12)plt.text(1.02, 0.5, r'$\alpha\_2$', fontsize=12)plt.xlim(-0.1, 1.1)plt.ylim(-1.1, 1.1)plt.show() |
| ![](images/image-1.png) | 该代码是c语言，最终运行结果是什么？                          |
| ![](images/image-2.png) | 图中的结果是哪个选项代码生成的？A：#include \<stdio.h>int main() {    const int TOTAL = 999;    const int MIDDLE = TOTAL / 2;  // 中间位置为499（索引从0开始）    for (int i = 0; i < TOTAL; i++) {        if (i == MIDDLE) {            printf("良");  // 中间位置输出"良"        } else {            printf("良");  // 其他位置输出"良"        }    }    return 0;}    B：#include \<stdio.h>int main() {    const int TOTAL = 999;    const int MIDDLE = TOTAL / 2;  // 中间位置为499（索引从0开始）    for (int i = 0; i < TOTAL; i++) {        if (i == MIDDLE) {            printf("艮");  // 中间位置输出"艮"        } else {            printf("良");  // 其他位置输出"良"        }    }    return 0;} |

## 主观题

| 出题思路                  | 图片                       | 问题                         |
| --------------------- | ------------------------ | -------------------------- |
| 根据流程图写代码              | ![](images/image-3.png)  | 写mermaid代码画出与图中流程图内容相同的流程图 |
| 给目标图片，问题让写代码得到这个图片的结果 | ![](images/Figure_1.png) | 我用python3怎么画出一模一样的图？       |
| 代码的OCR识别，尤其是抗干扰的识别    | ![](images/image-4.png)  | 这个代码存在哪些问题？                |