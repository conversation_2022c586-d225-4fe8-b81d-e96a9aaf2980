import turtle
import math

def draw_graphics_C():
    """
    This function draws a shape with a square, a circle, radial lines,
    and other shapes corresponding to image C.
    """
    # Screen setup
    screen = turtle.Screen()
    screen.title("Geometric Shape C")
    screen.bgcolor("white")
    screen.setup(width=600, height=600)

    # Turtle setup
    pen = turtle.Turtle()
    pen.speed(0)  # Set speed to fastest
    pen.pensize(2)

    # Define dimensions
    SQUARE_SIDE = 400
    LARGE_CIRCLE_RADIUS = SQUARE_SIDE / 2 - 10
    DOT_RADIUS = 10  # Radius for the white circle

    # --- Draw the outer square ---
    pen.penup()
    pen.goto(-SQUARE_SIDE / 2, -SQUARE_SIDE / 2)
    pen.pendown()
    for _ in range(4):
        pen.forward(SQUARE_SIDE)
        pen.left(90)

    # --- Draw the large circle ---
    pen.penup()
    pen.goto(0, -LARGE_CIRCLE_RADIUS)
    pen.pendown()
    pen.circle(LARGE_CIRCLE_RADIUS)

    # --- Draw the 8 radial lines ---
    pen.penup()
    pen.goto(0, 0)
    for i in range(8):
        pen.setheading(i * 45)
        pen.pendown()
        pen.forward(LARGE_CIRCLE_RADIUS)
        pen.penup()
        pen.goto(0, 0)

    # --- Draw the black circle
    distance = LARGE_CIRCLE_RADIUS * 0.6

    pen.penup()
    pen.goto(0, LARGE_CIRCLE_RADIUS - DOT_RADIUS)
    pen.dot(DOT_RADIUS * 2, "black")


    # --- Draw the white circle---
    angle_white = 90
    rad_white = math.radians(angle_white)
    x_white = distance * math.cos(rad_white)
    y_white = distance * math.sin(rad_white)

    pen.penup()

    pen.goto(x_white, y_white - DOT_RADIUS)
    pen.setheading(0)
    pen.pendown()
    pen.fillcolor("white")
    pen.begin_fill()
    pen.circle(DOT_RADIUS)
    pen.end_fill()


    # Hide the turtle and finish
    pen.hideturtle()
    screen.mainloop()

if __name__ == "__main__":
    draw_graphics_C() 