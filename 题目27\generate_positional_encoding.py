import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.colors import LinearSegmentedColormap
import seaborn as sns
from mpl_toolkits.mplot3d import Axes3D
import warnings
warnings.filterwarnings('ignore')

plt.rcParams['font.family'] = 'SimHei'
plt.rcParams['axes.unicode_minus'] = False

def sinusoidal_positional_encoding(seq_len, d_model):
    """
    生成正弦位置编码
    """
    pe = np.zeros((seq_len, d_model))
    position = np.arange(0, seq_len).reshape(-1, 1)
    
    # 计算除数项
    div_term = np.exp(np.arange(0, d_model, 2) * -(np.log(10000.0) / d_model))
    
    # 偶数位置使用sin，奇数位置使用cos
    pe[:, 0::2] = np.sin(position * div_term)
    pe[:, 1::2] = np.cos(position * div_term)
    
    return pe

def learned_positional_encoding(seq_len, d_model, seed=42):
    """
    生成学习式位置编码（模拟）
    """
    np.random.seed(seed)
    # 模拟训练后的位置编码，具有一定的结构性
    pe = np.random.randn(seq_len, d_model) * 0.1
    
    # 添加一些结构化模式
    for i in range(seq_len):
        for j in range(d_model):
            # 添加位置相关的模式
            pe[i, j] += 0.3 * np.sin(2 * np.pi * i / seq_len + j * 0.1)
            pe[i, j] += 0.2 * np.cos(np.pi * i / (seq_len/4) + j * 0.05)
    
    return pe

def rotary_positional_encoding(seq_len, d_model):
    """
    生成旋转位置编码（RoPE）的可视化表示
    """
    pe = np.zeros((seq_len, d_model))
    
    for pos in range(seq_len):
        for i in range(0, d_model, 2):
            theta = pos / (10000 ** (2 * i / d_model))
            pe[pos, i] = np.cos(theta)
            if i + 1 < d_model:
                pe[pos, i + 1] = np.sin(theta)
    
    return pe

def create_comprehensive_visualization():
    """
    创建综合的位置编码可视化
    """
    seq_len = 128
    d_model = 64
    
    # 生成三种位置编码
    sin_pe = sinusoidal_positional_encoding(seq_len, d_model)
    learned_pe = learned_positional_encoding(seq_len, d_model)
    rope_pe = rotary_positional_encoding(seq_len, d_model)
    
    # 创建复杂的多子图布局
    fig = plt.figure(figsize=(20, 16))
    
    # 自定义颜色映射
    colors = ['#2E86AB', '#A23B72', '#F18F01', '#C73E1D']
    custom_cmap = LinearSegmentedColormap.from_list('custom', colors, N=256)
    
    # 1. 正弦位置编码热力图
    ax1 = plt.subplot(3, 4, 1)
    im1 = ax1.imshow(sin_pe.T, cmap=custom_cmap, aspect='auto', interpolation='bilinear')
    ax1.set_title('正弦位置编码\n(Sinusoidal PE)', fontsize=12, fontweight='bold')
    ax1.set_xlabel('序列位置')
    ax1.set_ylabel('嵌入维度')
    plt.colorbar(im1, ax=ax1, shrink=0.8)
    
    # 2. 学习式位置编码热力图
    ax2 = plt.subplot(3, 4, 2)
    im2 = ax2.imshow(learned_pe.T, cmap='viridis', aspect='auto', interpolation='bilinear')
    ax2.set_title('学习式位置编码\n(Learned PE)', fontsize=12, fontweight='bold')
    ax2.set_xlabel('序列位置')
    ax2.set_ylabel('嵌入维度')
    plt.colorbar(im2, ax=ax2, shrink=0.8)
    
    # 3. 旋转位置编码热力图
    ax3 = plt.subplot(3, 4, 3)
    im3 = ax3.imshow(rope_pe.T, cmap='plasma', aspect='auto', interpolation='bilinear')
    ax3.set_title('旋转位置编码\n(RoPE)', fontsize=12, fontweight='bold')
    ax3.set_xlabel('序列位置')
    ax3.set_ylabel('嵌入维度')
    plt.colorbar(im3, ax=ax3, shrink=0.8)
    
    # 4. 位置编码相似性矩阵
    ax4 = plt.subplot(3, 4, 4)
    # 计算正弦位置编码的相似性矩阵
    similarity = np.dot(sin_pe, sin_pe.T)
    im4 = ax4.imshow(similarity, cmap='RdYlBu_r', aspect='auto')
    ax4.set_title('位置相似性矩阵\n(正弦编码)', fontsize=12, fontweight='bold')
    ax4.set_xlabel('位置 i')
    ax4.set_ylabel('位置 j')
    plt.colorbar(im4, ax=ax4, shrink=0.8)
    
    # 5. 特定维度的波形对比
    ax5 = plt.subplot(3, 4, 5)
    positions = np.arange(seq_len)
    ax5.plot(positions, sin_pe[:, 0], 'b-', linewidth=2, label='维度0 (sin)', alpha=0.8)
    ax5.plot(positions, sin_pe[:, 1], 'r--', linewidth=2, label='维度1 (cos)', alpha=0.8)
    ax5.plot(positions, sin_pe[:, 8], 'g:', linewidth=2, label='维度8 (sin)', alpha=0.8)
    ax5.plot(positions, sin_pe[:, 16], 'm-.', linewidth=2, label='维度16 (sin)', alpha=0.8)
    ax5.set_title('正弦编码波形分析', fontsize=12, fontweight='bold')
    ax5.set_xlabel('序列位置')
    ax5.set_ylabel('编码值')
    ax5.legend(fontsize=9)
    ax5.grid(True, alpha=0.3)
    
    # 6. 频率分析
    ax6 = plt.subplot(3, 4, 6)
    freqs = []
    for i in range(0, d_model, 2):
        freq = 1 / (10000 ** (2 * i / d_model))
        freqs.append(freq)
    
    dim_indices = np.arange(0, d_model, 2)
    ax6.semilogy(dim_indices, freqs, 'o-', linewidth=2, markersize=6, color='purple')
    ax6.set_title('位置编码频率分布', fontsize=12, fontweight='bold')
    ax6.set_xlabel('维度索引')
    ax6.set_ylabel('频率 (对数尺度)')
    ax6.grid(True, alpha=0.3)
    
    # 7. 3D可视化 - 正弦位置编码的前几个维度
    ax7 = plt.subplot(3, 4, 7, projection='3d')
    pos_sample = positions[::4]  # 采样以减少密度
    X, Y = np.meshgrid(pos_sample, np.arange(8))
    Z = sin_pe[::4, :8].T
    
    surf = ax7.plot_surface(X, Y, Z, cmap='coolwarm', alpha=0.8, 
                           linewidth=0, antialiased=True)
    ax7.set_title('3D位置编码景观\n(前8维)', fontsize=10, fontweight='bold')
    ax7.set_xlabel('位置')
    ax7.set_ylabel('维度')
    ax7.set_zlabel('编码值')
    
    # 8. 位置距离衰减分析
    ax8 = plt.subplot(3, 4, 8)
    reference_pos = 32  # 参考位置
    distances = []
    similarities = []
    
    for pos in range(seq_len):
        # 计算欧几里得距离
        dist = np.linalg.norm(sin_pe[pos] - sin_pe[reference_pos])
        distances.append(abs(pos - reference_pos))
        similarities.append(1 / (1 + dist))  # 相似性
    
    ax8.scatter(distances, similarities, c=distances, cmap='viridis', alpha=0.7, s=30)
    ax8.set_title(f'位置{reference_pos}的相似性衰减', fontsize=12, fontweight='bold')
    ax8.set_xlabel('位置距离')
    ax8.set_ylabel('相似性')
    ax8.grid(True, alpha=0.3)
    
    # 9. 不同编码方法的统计对比
    ax9 = plt.subplot(3, 4, 9)
    methods = ['正弦编码', '学习编码', '旋转编码']
    means = [np.mean(np.abs(sin_pe)), np.mean(np.abs(learned_pe)), np.mean(np.abs(rope_pe))]
    stds = [np.std(sin_pe), np.std(learned_pe), np.std(rope_pe)]
    
    bars = ax9.bar(methods, means, yerr=stds, capsize=5, 
                   color=['skyblue', 'lightcoral', 'lightgreen'], alpha=0.8)
    ax9.set_title('编码方法统计对比', fontsize=12, fontweight='bold')
    ax9.set_ylabel('平均绝对值 ± 标准差')
    ax9.tick_params(axis='x', rotation=45)

    # 10. 注意力权重模拟
    ax10 = plt.subplot(3, 4, 10)
    # 模拟查询位置64对所有位置的注意力权重
    query_pos = 64
    attention_weights = []

    for key_pos in range(seq_len):
        # 简化的注意力计算：点积相似性
        similarity = np.dot(sin_pe[query_pos], sin_pe[key_pos])
        attention_weights.append(np.exp(similarity))

    # 归一化
    attention_weights = np.array(attention_weights)
    attention_weights = attention_weights / np.sum(attention_weights)

    ax10.plot(positions, attention_weights, 'r-', linewidth=2, alpha=0.8)
    ax10.axvline(x=query_pos, color='blue', linestyle='--', alpha=0.7, label=f'查询位置 {query_pos}')
    ax10.set_title('模拟注意力权重分布', fontsize=12, fontweight='bold')
    ax10.set_xlabel('键位置')
    ax10.set_ylabel('注意力权重')
    ax10.legend()
    ax10.grid(True, alpha=0.3)

    # 11. 位置编码梯度分析
    ax11 = plt.subplot(3, 4, 11)
    # 计算位置编码的梯度（差分）
    gradient = np.diff(sin_pe, axis=0)
    gradient_norm = np.linalg.norm(gradient, axis=1)

    ax11.plot(positions[1:], gradient_norm, 'g-', linewidth=2, alpha=0.8)
    ax11.set_title('位置编码梯度范数', fontsize=12, fontweight='bold')
    ax11.set_xlabel('序列位置')
    ax11.set_ylabel('梯度范数')
    ax11.grid(True, alpha=0.3)

    # 12. 周期性分析
    ax12 = plt.subplot(3, 4, 12)
    # 分析不同维度的周期性
    periods = []
    for dim in range(0, min(16, d_model), 2):
        signal = sin_pe[:, dim]
        # 简单的周期检测：找到自相关的峰值
        autocorr = np.correlate(signal, signal, mode='full')
        autocorr = autocorr[autocorr.size // 2:]

        # 找到第一个显著峰值（排除零延迟）
        peaks = []
        for i in range(1, len(autocorr) - 1):
            if autocorr[i] > autocorr[i-1] and autocorr[i] > autocorr[i+1]:
                if autocorr[i] > 0.5 * autocorr[0]:  # 阈值
                    peaks.append(i)

        if peaks:
            periods.append(peaks[0])
        else:
            periods.append(seq_len)  # 如果没找到周期，设为序列长度

    dim_labels = [f'维度{i}' for i in range(0, min(16, d_model), 2)]
    bars = ax12.bar(range(len(periods)), periods, color='orange', alpha=0.7)
    ax12.set_title('各维度周期性分析', fontsize=12, fontweight='bold')
    ax12.set_xlabel('维度')
    ax12.set_ylabel('检测到的周期长度')
    ax12.set_xticks(range(len(periods)))
    ax12.set_xticklabels(dim_labels, rotation=45, fontsize=8)

    plt.tight_layout(pad=2.0)
    plt.savefig('C:/Users/<USER>/OneDrive/Desktop/VLM prompt/VLM-code/code/题目27/transformer_positional_encoding_analysis.png',
                dpi=300, bbox_inches='tight', facecolor='white')
    plt.show()

    return sin_pe, learned_pe, rope_pe

if __name__ == "__main__":
    sin_pe, learned_pe, rope_pe = create_comprehensive_visualization()
    print("位置编码可视化已生成完成！")
    print(f"正弦位置编码形状: {sin_pe.shape}")
    print(f"学习位置编码形状: {learned_pe.shape}")
    print(f"旋转位置编码形状: {rope_pe.shape}")
