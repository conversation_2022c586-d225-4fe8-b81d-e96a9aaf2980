根据以下要求，使用HTML、CSS和JavaScript复现图中的音乐播放器：

除了播放本地文件，还应当通过load链接播放云端文件。

除了可以播放音乐和图中的功能，背景还要有实时音频频谱可视化（柱状图形式），并有围绕中心旋转的例子和中心发光脉冲。

要求使用原生JavaScript，不依赖外部库，所有代码整合到一个html文件中



推理过程：1）界面布局分析：观察图片发现左右分栏布局，左侧唱片区域，右侧控制面板，使用CSS实现；2）视觉风格确定：深色主题配紫色渐变，使用CSS变量定义颜色系统，backdrop-filter: blur(10px) 实现毛玻璃效果；3）唱片动画实现：圆形旋转唱片通过CSS @keyframes spin 动画，conic-gradient 创建纹理，播放时控制动画状态；4）播放控制功能：根据按钮布局创建HTML元素，使用 audio API的 play()、pause()、currentTime 等方法控制播放；5）进度条和滑块：使用 input[type="range"]实现可拖拽控件，CSS变量 --progress 动态显示进度，对应 audio.volume 和 audio.playbackRate 属性；6）音频效果处理：实现Echo效果，BiquadFilterNode 实现Bass Boost，各参数对应界面滑块控制；7）频谱可视化： AnalyserNode 获取频域数据，Canvas绘制96个柱状条，高度映射频率强度，颜色根据频率位置变化；8）粒子和发光效果：粒子数组存储运动属性，圆周运动轨迹计算；多层半透明圆环实现中心发光，使用 globalCompositeOperation 混合模式；9）动态配色系统：频域数据分段映射到HSL色相，实现音频驱动的颜色变化，支持自动和手动颜色模式；10）交互功能完善：拖拽上传监听 drop 事件，URL加载使用 fetch() API，键盘快捷键监听 keydown 事件，响应式布局使用媒体查询适配不同屏幕。